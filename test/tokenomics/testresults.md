npm run test:tokenomics

> WarpSector-game@1.0.0 test:tokenomics
> node test/tokenomics/runStressTest.js

🚀 Starting WarpSector Tokenomics Stress Test
============================================================
📋 Test Configuration:
   Duration: 300 seconds
   Max Users: 10
   API URL: http://localhost:3001/api
   Hardhat URL: http://localhost:8545
   Hot Wallet: ******************************************
   Scenario: All scenarios
   Verbose: Disabled

🔍 Validating prerequisites...
   ✅ Game Server: OK
   ✅ Hardhat Node: OK
   ✅ Hot Wallet Balance: OK
   ✅ Test Accounts: OK
✅ All prerequisites validated

🔍 DEBUG: Connecting to Hardhat at: http://localhost:8545
🔍 DEBUG: Hot wallet address: ******************************************
🧪 TokenomicsStressTest initialized
📊 Configuration: {
  apiBaseUrl: 'http://localhost:3001/api',
  hardhatUrl: 'http://localhost:8545',
  chainId: 31337,
  hotWalletAddress: '******************************************',
  testDuration: 300000,
  maxConcurrentUsers: 10,
  verbose: false,
  reportFile: null,
  scenario: null
}
🚀 Initializing Tokenomics Stress Test Framework...
✅ Server health check passed: { status: 'OK', message: 'AI Service Server is running' }
🔍 DEBUG: Provider connected - Current block number: 0
🔗 Connected to network: Network {}
🔍 DEBUG: Reading balance for address: ******************************************
🔍 DEBUG: Expected address: ******************************************
🔍 DEBUG: Addresses match: true
🔍 DEBUG: Provider URL: http://localhost:8545
🔍 DEBUG: Making direct RPC call to http://localhost:8545
🔍 DEBUG: Direct RPC result: {"jsonrpc":"2.0","id":99,"result":"0x152d02c7e14af6800000"}
🔍 DEBUG: Direct RPC balance in wei: 100000000000000000000000
🔍 DEBUG: Direct RPC balance in ETH: 100000.0 ETH
🔍 DEBUG: Expected 100,000 ETH in hex: 0x21e19e0c9bab2400000
🔍 DEBUG: Actual result hex: 0x152d02c7e14af6800000
🔍 DEBUG: Balances match expected: false
🔍 DEBUG: Balance read attempt 1/3
🔍 DEBUG: Attempt 1 - Raw balance in wei: 100000000000000000000000
🔍 DEBUG: Attempt 1 - Formatted balance: 100000.0 ETH
🔍 DEBUG: Got reasonable balance on attempt 1: 100000 ETH
🔍 DEBUG: Final balance in wei: 100000000000000000000000
🔍 DEBUG: Expected 100,000 ETH = 0x56bc75e2d63100000 wei = 100000000000000000000000 wei
🔍 DEBUG: Final formatted balance: 100000.0 ETH
💰 Hot wallet balance: 100000.0 ETH
👥 Found 20 test accounts
🔍 DEBUG: Connected to network - Chain ID: 31337, Name: unknown
🔍 DEBUG: Expected Chain ID: 31337
📊 TransactionTracker initialized
💰 TreasuryMonitor initialized
📊 Monitoring systems initialized
✅ Initialized daily reward tracker for user: ******************************************
🧪 grinder_1: ETH Test Mode enabled on TokenEconomyManager - will use ETH pricing with 90% discount
👤 Created grinder user simulator: grinder_1 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 grinder_2: ETH Test Mode enabled on TokenEconomyManager - will use ETH pricing with 90% discount
👤 Created grinder user simulator: grinder_2 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 grinder_3: ETH Test Mode enabled on TokenEconomyManager - will use ETH pricing with 90% discount
👤 Created grinder user simulator: grinder_3 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 whale_1: ETH Test Mode enabled on TokenEconomyManager - will use ETH pricing with 90% discount
👤 Created whale user simulator: whale_1 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 whale_2: ETH Test Mode enabled on TokenEconomyManager - will use ETH pricing with 90% discount
👤 Created whale user simulator: whale_2 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 creator_1: ETH Test Mode enabled on TokenEconomyManager - will use ETH pricing with 90% discount
👤 Created creator user simulator: creator_1 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 creator_2: ETH Test Mode enabled on TokenEconomyManager - will use ETH pricing with 90% discount
👤 Created creator user simulator: creator_2 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 casual_1: ETH Test Mode enabled on TokenEconomyManager - will use ETH pricing with 90% discount
👤 Created casual user simulator: casual_1 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 casual_2: ETH Test Mode enabled on TokenEconomyManager - will use ETH pricing with 90% discount
👤 Created casual user simulator: casual_2 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 casual_3: ETH Test Mode enabled on TokenEconomyManager - will use ETH pricing with 90% discount
👤 Created casual user simulator: casual_3 (******************************************)
💰 Initializing wallet balances for ETH test mode...
💰 grinder_1: Wallet balance initialized: 10000 ETH
💰 grinder_2: Wallet balance initialized: 10000 ETH
💰 grinder_3: Wallet balance initialized: 10000 ETH
💰 whale_1: Wallet balance initialized: 10000 ETH
💰 whale_2: Wallet balance initialized: 10000 ETH
💰 creator_1: Wallet balance initialized: 10000 ETH
💰 creator_2: Wallet balance initialized: 10000 ETH
💰 casual_1: Wallet balance initialized: 10000 ETH
💰 casual_2: Wallet balance initialized: 10000 ETH
💰 casual_3: Wallet balance initialized: 10000 ETH
👥 Created 10 user simulators
✅ Stress test framework initialized successfully
🚀 Starting Tokenomics Stress Test...
🔍 TreasuryMonitor: Getting initial balance...
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 100000000000000000000000 wei = 100000 ETH
💰 Initial treasury balance: 100000 ETH
🔍 TreasuryMonitor: Balance set to 100000 ETH
🚀 TreasuryMonitor started
🚀 TransactionTracker started
📊 Monitoring started
🔍 Queue health monitoring started
🔄 Running test scenarios sequentially to prevent server overload...
🎮 Running Sequential Grinding Test (Attack Vector 1)...
📋 Objective: Test if grinder earnings can drain treasury
👥 Testing with 3 grinder accounts
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 100000000000000000000000 wei = 100000 ETH
💰 Initial treasury balance: 100000 ETH
🎯 Starting grinder session: grinder_1
⏳ Grinder grinder_1 waiting 1500ms to prevent server overload...
🎮 Level 1: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 53 ETH
🧪 ETH Test Mode: Processing level_completion reward of 53 ETH
🧪 ETH Test Mode: Level completion reward - sending 53 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x319f37ed3b5e8f492e72eb8de96bba4e8387d05c5ee1e784f410682aa1596b92
✅ Level 1 completed! PARTIAL: 53 ETH
✅ Level 1 completed! PARTIAL: 53 ETH
🎁 Game awarded 53 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 99946*********000000000 wei = 99946.********* ETH
💰 Balance change: -53.000039 ETH (99946.999961 ETH total)
📉 Treasury outflow detected: -53.000039 ETH
🎮 Level 2: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 75 ETH
🧪 ETH Test Mode: Processing level_completion reward of 75 ETH
🧪 ETH Test Mode: Level completion reward - sending 75 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x71cb581b7a086e2c02173b80459c4dcbd60c4edf42476078a9f35eeb4ec7889f
✅ Level 2 completed! PARTIAL: 75 ETH
✅ Level 2 completed! PARTIAL: 75 ETH
🎁 Game awarded 75 ETH
🎮 Level 3: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 104 ETH
🧪 ETH Test Mode: Processing level_completion reward of 104 ETH
🧪 ETH Test Mode: Level completion reward - sending 104 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x83f0e4682da74aa7447073fdb813f2208620cb5adaeee0ac9b93897e54ef4c43
✅ Level 3 completed! PARTIAL: 104 ETH
✅ Level 3 completed! PARTIAL: 104 ETH
🎁 Game awarded 104 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 99767999888469672084000 wei = 99767.99988846967 ETH
💰 Balance change: -179.000072 ETH (99767.999888 ETH total)
📉 Treasury outflow detected: -179.000072 ETH
🎮 Level 4: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 85 ETH
🧪 ETH Test Mode: Processing level_completion reward of 85 ETH
🧪 ETH Test Mode: Level completion reward - sending 85 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xae8136b65af68a5facb3b5a3ac561083d4f2db1b73e532292efe309b102eb423
✅ Level 4 completed! PARTIAL: 85 ETH
✅ Level 4 completed! PARTIAL: 85 ETH
🎁 Game awarded 85 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 99682999855152470243000 wei = 99682.99985515248 ETH
💰 Balance change: -85.000033 ETH (99682.999855 ETH total)
📉 Treasury outflow detected: -85.000033 ETH
🎮 Level 5: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 125 ETH
🧪 ETH Test Mode: Processing level_completion reward of 125 ETH
🧪 ETH Test Mode: Level completion reward - sending 125 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xbd8fbcf7d6a4fd1d48b925de2bca50de3c47f5abdfbcd07204da7cc2be70e9e3
✅ Level 5 completed! PARTIAL: 125 ETH
✅ Level 5 completed! PARTIAL: 125 ETH
🎁 Game awarded 125 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 99557999823372763116000 wei = 99557.99982337277 ETH
💰 Balance change: -125.000032 ETH (99557.999823 ETH total)
📉 Treasury outflow detected: -125.000032 ETH
🎮 Level 6: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 115 ETH
🧪 ETH Test Mode: Processing level_completion reward of 115 ETH
🧪 ETH Test Mode: Level completion reward - sending 115 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xd4450a82c63cd84552a78cd1bf83ab0c3b0ac8dc18aa425ef77851690b9e7042
✅ Level 6 completed! PARTIAL: 115 ETH
✅ Level 6 completed! PARTIAL: 115 ETH
🎁 Game awarded 115 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 99442999792938632921000 wei = 99442.99979293863 ETH
💰 Balance change: -115.000030 ETH (99442.999793 ETH total)
📉 Treasury outflow detected: -115.000030 ETH
🎮 Level 7: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 87 ETH
🧪 ETH Test Mode: Processing level_completion reward of 87 ETH
🧪 ETH Test Mode: Level completion reward - sending 87 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x5f901b0c0ac54c21243c2023c3ec917d35505f5390fc038d8a3734ed7fb8c7ca
✅ Level 7 completed! PARTIAL: 87 ETH
✅ Level 7 completed! PARTIAL: 87 ETH
🎁 Game awarded 87 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 99355999763682118025000 wei = 99355.99976368212 ETH
💰 Balance change: -87.000029 ETH (99355.999764 ETH total)
📉 Treasury outflow detected: -87.000029 ETH
🎮 Level 8: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 135 ETH
🧪 ETH Test Mode: Processing level_completion reward of 135 ETH
🧪 ETH Test Mode: Level completion reward - sending 135 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x4104722c93f1ae6721a7b02f7eeec7c587dec0810cdae190dce0d57507460e96
✅ Level 8 completed! PARTIAL: 135 ETH
✅ Level 8 completed! PARTIAL: 135 ETH
🎁 Game awarded 135 ETH
🎮 Level 9: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 118 ETH
🧪 ETH Test Mode: Processing level_completion reward of 118 ETH
🧪 ETH Test Mode: Level completion reward - sending 118 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xcfd821900461df05459a095c0ca3137048e5087c5b94b2e655deab938a89d6e8
✅ Level 9 completed! PARTIAL: 118 ETH
✅ Level 9 completed! PARTIAL: 118 ETH
🎁 Game awarded 118 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 99102999708132299533000 wei = 99102.9997081323 ETH
💰 Balance change: -253.000056 ETH (99102.999708 ETH total)
📉 Treasury outflow detected: -253.000056 ETH
🎮 Level 10: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 129 ETH
🧪 ETH Test Mode: Processing level_completion reward of 129 ETH
🧪 ETH Test Mode: Level completion reward - sending 129 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x59c6e6e4614e3b5ed2754714616031bdb1494e13289cf461724bd5cb90097683
✅ Level 10 completed! PARTIAL: 129 ETH
✅ Level 10 completed! PARTIAL: 129 ETH
🎁 Game awarded 129 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 98973999681597760159000 wei = 98973.99968159776 ETH
💰 Balance change: -129.000027 ETH (98973.999682 ETH total)
📉 Treasury outflow detected: -129.000027 ETH
🏆 Grinder grinder_1 completed 10 levels with REAL rewards
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 98973999681597760159000 wei = 98973.99968159776 ETH
📊 Treasury impact after grinder_1: 1026.000318 ETH
👤 Running casual player session: casual_1
🎮 Level 1: Simulating moderate completion (60%)...
🧪 ETH Test Mode: Applied 90% discount to reward: 87 ETH
🧪 ETH Test Mode: Processing level_completion reward of 87 ETH
🧪 ETH Test Mode: Level completion reward - sending 87 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x4f93c65f9f826d38f449e2c8d534998ef988067beebaa62a8360cfd5a1812887
✅ Level 1 completed! PARTIAL: 87 ETH
❌ Level 1 completion failed: Cannot read properties of undefined (reading 'isTestMode')
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 98886999655754069650000 wei = 98886.99965575407 ETH
💰 Balance change: -87.000026 ETH (98886.999656 ETH total)
📉 Treasury outflow detected: -87.000026 ETH
🎮 Level 2: Simulating moderate completion (60%)...
🧪 ETH Test Mode: Applied 90% discount to reward: 49 ETH
🧪 ETH Test Mode: Processing level_completion reward of 49 ETH
🧪 ETH Test Mode: Level completion reward - sending 49 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x1555309f63ced57b0820a861e7eb23febe70c535ab1511b44076fdf47f514948
✅ Level 2 completed! PARTIAL: 49 ETH
❌ Level 2 completion failed: Cannot read properties of undefined (reading 'isTestMode')
🎮 Level 3: Simulating moderate completion (60%)...
🧪 ETH Test Mode: Applied 90% discount to reward: 103 ETH
🧪 ETH Test Mode: Processing level_completion reward of 103 ETH
🧪 ETH Test Mode: Level completion reward - sending 103 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x0b5bdd90697accfb70c7b3df8124f912c8c7e06d6b32196c4e7ec023f2fdd03d
✅ Level 3 completed! PARTIAL: 103 ETH
❌ Level 3 completion failed: Cannot read properties of undefined (reading 'isTestMode')
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 98734999605805058720000 wei = 98734.99960580506 ETH
💰 Balance change: -152.000050 ETH (98734.999606 ETH total)
📉 Treasury outflow detected: -152.000050 ETH
🎮 Level 4: Simulating moderate completion (60%)...
🧪 ETH Test Mode: Applied 90% discount to reward: 86 ETH
🧪 ETH Test Mode: Processing level_completion reward of 86 ETH
🧪 ETH Test Mode: Level completion reward - sending 86 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xa483c2f34faa1a94e60374ec6209340c8f407d0c2db536486be8a569a24a0be8
✅ Level 4 completed! PARTIAL: 86 ETH
❌ Level 4 completion failed: Cannot read properties of undefined (reading 'isTestMode')
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 98648999581558217136000 wei = 98648.99958155822 ETH
💰 Balance change: -86.000024 ETH (98648.999582 ETH total)
📉 Treasury outflow detected: -86.000024 ETH
🎮 Level 5: Simulating moderate completion (60%)...
🧪 ETH Test Mode: Applied 90% discount to reward: 97 ETH
🧪 ETH Test Mode: Processing level_completion reward of 97 ETH
🧪 ETH Test Mode: Level completion reward - sending 97 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x30d6b7853694453040cad62ecc744087b6ee6404b5bf04569d528d06f53c6dcc
✅ Level 5 completed! PARTIAL: 97 ETH
❌ Level 5 completion failed: Cannot read properties of undefined (reading 'isTestMode')
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 98551999557716662532000 wei = 98551.99955771666 ETH
💰 Balance change: -97.000024 ETH (98551.999558 ETH total)
📉 Treasury outflow detected: -97.000024 ETH
🎮 Level 6: Simulating moderate completion (60%)...
🧪 ETH Test Mode: Applied 90% discount to reward: 105 ETH
🧪 ETH Test Mode: Processing level_completion reward of 105 ETH
🧪 ETH Test Mode: Level completion reward - sending 105 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xb63f02a2fee107ad81a256b8c584421d0134f6036166af21c1a1792e12a3477e
✅ Level 6 completed! PARTIAL: 105 ETH
❌ Level 6 completion failed: Cannot read properties of undefined (reading 'isTestMode')
🔍 Reading balance for hot wallet: ******************************************
🎮 Level 7: Simulating moderate completion (60%)...
🧪 ETH Test Mode: Applied 90% discount to reward: 153 ETH
🧪 ETH Test Mode: Processing level_completion reward of 153 ETH
🧪 ETH Test Mode: Level completion reward - sending 153 ETH from hot wallet to player ******************************************
🔍 Raw balance: 98446999534229804963000 wei = 98446.9995342298 ETH
💰 Balance change: -105.000023 ETH (98446.999534 ETH total)
📉 Treasury outflow detected: -105.000023 ETH
✅ ETH Test Mode: Hot wallet transaction successful: 0x1e38b8f5197eff68977071ccb25b3b14844f45b87584a71dbc4f40c35d710635
✅ Level 7 completed! PARTIAL: 153 ETH
❌ Level 7 completion failed: Cannot read properties of undefined (reading 'isTestMode')
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 98293999511053369373000 wei = 98293.99951105336 ETH
💰 Balance change: -153.000023 ETH (98293.999511 ETH total)
📉 Treasury outflow detected: -153.000023 ETH
🎯 Casual player casual_1 completed session
🎯 Starting grinder session: grinder_2
⏳ Grinder grinder_2 waiting 3000ms to prevent server overload...
🎮 Level 1: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 44 ETH
🧪 ETH Test Mode: Processing level_completion reward of 44 ETH
🧪 ETH Test Mode: Level completion reward - sending 44 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x54b9a403920aa2abb7fe5710926d0f9983670b67654bdaa90be0a474215008dd
✅ Level 1 completed! PARTIAL: 44 ETH
✅ Level 1 completed! PARTIAL: 44 ETH
🎁 Game awarded 44 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 98249999488148607339000 wei = 98249.9994881486 ETH
💰 Balance change: -44.000023 ETH (98249.999488 ETH total)
📉 Treasury outflow detected: -44.000023 ETH
🎮 Level 2: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 51 ETH
🧪 ETH Test Mode: Processing level_completion reward of 51 ETH
🧪 ETH Test Mode: Level completion reward - sending 51 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x5d5b24d078c8c233ea79f4127c3454612dddd5f2fd4e87d2911856424eeec502
✅ Level 2 completed! PARTIAL: 51 ETH
✅ Level 2 completed! PARTIAL: 51 ETH
🎁 Game awarded 51 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 98198999465481607221000 wei = 98198.99946548161 ETH
💰 Balance change: -51.000023 ETH (98198.999465 ETH total)
📉 Treasury outflow detected: -51.000023 ETH
🎮 Level 3: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 95 ETH
🧪 ETH Test Mode: Processing level_completion reward of 95 ETH
🧪 ETH Test Mode: Level completion reward - sending 95 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x47c8764b66c7d8ec6ecd09ae0532badabd04b3a89e6804e83efe6400028cfde1
✅ Level 3 completed! PARTIAL: 95 ETH
✅ Level 3 completed! PARTIAL: 95 ETH
🎁 Game awarded 95 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 98103999443022690391000 wei = 98103.9994430227 ETH
💰 Balance change: -95.000022 ETH (98103.999443 ETH total)
📉 Treasury outflow detected: -95.000022 ETH
🎮 Level 4: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 112 ETH
🧪 ETH Test Mode: Processing level_completion reward of 112 ETH
🧪 ETH Test Mode: Level completion reward - sending 112 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x7ccf9a7f578db66dd84467b027c6aaa1ca26d4dc967cf9122723e321436ba94a
✅ Level 4 completed! PARTIAL: 112 ETH
✅ Level 4 completed! PARTIAL: 112 ETH
🎁 Game awarded 112 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 97991999420745882852000 wei = 97991.99942074588 ETH
💰 Balance change: -112.000022 ETH (97991.999421 ETH total)
📉 Treasury outflow detected: -112.000022 ETH
🎮 Level 5: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 71 ETH
🧪 ETH Test Mode: Processing level_completion reward of 71 ETH
🧪 ETH Test Mode: Level completion reward - sending 71 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xe94d1ebe5f7de581a945d55624ea94a6a7b0d5fcb89d64fe2360408496f14ab9
✅ Level 5 completed! PARTIAL: 71 ETH
✅ Level 5 completed! PARTIAL: 71 ETH
🎁 Game awarded 71 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 97920999398628452797000 wei = 97920.99939862845 ETH
💰 Balance change: -71.000022 ETH (97920.999399 ETH total)
📉 Treasury outflow detected: -71.000022 ETH
🎮 Level 6: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 115 ETH
🧪 ETH Test Mode: Processing level_completion reward of 115 ETH
🧪 ETH Test Mode: Level completion reward - sending 115 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xff14274c021225e691fa054b5c2bf5a113ca3fab4ee2a3cdc0144a60b6578d9c
✅ Level 6 completed! PARTIAL: 115 ETH
✅ Level 6 completed! PARTIAL: 115 ETH
🎁 Game awarded 115 ETH
🎮 Level 7: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 122 ETH
🧪 ETH Test Mode: Processing level_completion reward of 122 ETH
🧪 ETH Test Mode: Level completion reward - sending 122 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x9739a0b47d4b6349267777b3b91ed37b1abe55411d34afbb133c787489bab6f6
✅ Level 7 completed! PARTIAL: 122 ETH
✅ Level 7 completed! PARTIAL: 122 ETH
🎁 Game awarded 122 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 97683999354794631283000 wei = 97683.99935479464 ETH
💰 Balance change: -237.000044 ETH (97683.999355 ETH total)
📉 Treasury outflow detected: -237.000044 ETH
🎮 Level 8: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 101 ETH
🧪 ETH Test Mode: Processing level_completion reward of 101 ETH
🧪 ETH Test Mode: Level completion reward - sending 101 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x525d014a344fedd7a1f565edd4461ce19f067b4f74a38414d8058661626a722b
✅ Level 8 completed! PARTIAL: 101 ETH
✅ Level 8 completed! PARTIAL: 101 ETH
🎁 Game awarded 101 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 97582999333045591166000 wei = 97582.99933304559 ETH
💰 Balance change: -101.000022 ETH (97582.999333 ETH total)
📉 Treasury outflow detected: -101.000022 ETH
🎮 Level 9: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 146 ETH
🧪 ETH Test Mode: Processing level_completion reward of 146 ETH
🧪 ETH Test Mode: Level completion reward - sending 146 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x79f67ec7296a5a2ae98b48fd0dbde82f8bb4154cd6ddbe369f9d38a7aba4eb7d
✅ Level 9 completed! PARTIAL: 146 ETH
✅ Level 9 completed! PARTIAL: 146 ETH
🎁 Game awarded 146 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 97436999311390049979000 wei = 97436.99931139006 ETH
💰 Balance change: -146.000022 ETH (97436.999311 ETH total)
📉 Treasury outflow detected: -146.000022 ETH
🎮 Level 10: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 92 ETH
🧪 ETH Test Mode: Processing level_completion reward of 92 ETH
🧪 ETH Test Mode: Level completion reward - sending 92 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xfc58a4c11a8ab6a9c44523bbe648b39507241c3782c5db3bda767396bc635d46
✅ Level 10 completed! PARTIAL: 92 ETH
✅ Level 10 completed! PARTIAL: 92 ETH
🎁 Game awarded 92 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 97344999289816336720000 wei = 97344.99928981633 ETH
💰 Balance change: -92.000022 ETH (97344.999290 ETH total)
📉 Treasury outflow detected: -92.000022 ETH
🏆 Grinder grinder_2 completed 10 levels with REAL rewards
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 97344999289816336720000 wei = 97344.99928981633 ETH
📊 Treasury impact after grinder_2: 2655.000710 ETH
👤 Running casual player session: casual_2
🎮 Level 1: Simulating moderate completion (60%)...
🧪 ETH Test Mode: Applied 90% discount to reward: 62 ETH
🧪 ETH Test Mode: Processing level_completion reward of 62 ETH
🧪 ETH Test Mode: Level completion reward - sending 62 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xffdabf5c9d2318fe5c333d7cba43c9402e81339475969a7c4202a2526af60008
✅ Level 1 completed! PARTIAL: 62 ETH
❌ Level 1 completion failed: Cannot read properties of undefined (reading 'isTestMode')
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 97282999268314237199000 wei = 97282.99926831423 ETH
💰 Balance change: -62.000022 ETH (97282.999268 ETH total)
📉 Treasury outflow detected: -62.000022 ETH
🎮 Level 2: Simulating moderate completion (60%)...
🧪 ETH Test Mode: Applied 90% discount to reward: 65 ETH
🧪 ETH Test Mode: Processing level_completion reward of 65 ETH
🧪 ETH Test Mode: Level completion reward - sending 65 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x92def170339c5ef5b731c0f0cf903d248ccd22d27f870885a44edc398f8e738e
✅ Level 2 completed! PARTIAL: 65 ETH
❌ Level 2 completion failed: Cannot read properties of undefined (reading 'isTestMode')
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 97217999246874812241000 wei = 97217.99924687481 ETH
💰 Balance change: -65.000021 ETH (97217.999247 ETH total)
📉 Treasury outflow detected: -65.000021 ETH
🎮 Level 3: Simulating moderate completion (60%)...
🧪 ETH Test Mode: Applied 90% discount to reward: 56 ETH
🧪 ETH Test Mode: Processing level_completion reward of 56 ETH
🧪 ETH Test Mode: Level completion reward - sending 56 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x3c0551ab8359e9a409c694a3154c99d477763b0b792f79425e1eaf55674ff5a2
✅ Level 3 completed! PARTIAL: 56 ETH
❌ Level 3 completion failed: Cannot read properties of undefined (reading 'isTestMode')
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 97161999225490238485000 wei = 97161.99922549023 ETH
💰 Balance change: -56.000021 ETH (97161.999225 ETH total)
📉 Treasury outflow detected: -56.000021 ETH
🎮 Level 4: Simulating moderate completion (60%)...
🧪 ETH Test Mode: Applied 90% discount to reward: 60 ETH
🧪 ETH Test Mode: Processing level_completion reward of 60 ETH
🧪 ETH Test Mode: Level completion reward - sending 60 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xb1cffd1a5c8e1a0b55f92cf868e17ba53718371403b013f81de99aa31952a278
✅ Level 4 completed! PARTIAL: 60 ETH
❌ Level 4 completion failed: Cannot read properties of undefined (reading 'isTestMode')
🎮 Level 5: Simulating moderate completion (60%)...
🧪 ETH Test Mode: Applied 90% discount to reward: 68 ETH
🧪 ETH Test Mode: Processing level_completion reward of 68 ETH
🧪 ETH Test Mode: Level completion reward - sending 68 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x75008c791e80abaf73deb41bd8196a64dbe14f7cf41c641ec8d60dcf48fb6dbe
✅ Level 5 completed! PARTIAL: 68 ETH
❌ Level 5 completion failed: Cannot read properties of undefined (reading 'isTestMode')
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 97033999182859112045000 wei = 97033.99918285912 ETH
💰 Balance change: -128.000043 ETH (97033.999183 ETH total)
📉 Treasury outflow detected: -128.000043 ETH
🎮 Level 6: Simulating moderate completion (60%)...
🧪 ETH Test Mode: Applied 90% discount to reward: 132 ETH
🧪 ETH Test Mode: Processing level_completion reward of 132 ETH
🧪 ETH Test Mode: Level completion reward - sending 132 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x1d7068d976014b62bd3d5d48da99b4a272caa902459b2c10d0ede781125fe09b
✅ Level 6 completed! PARTIAL: 132 ETH
❌ Level 6 completion failed: Cannot read properties of undefined (reading 'isTestMode')
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 96901999161601323038000 wei = 96901.99916160133 ETH
💰 Balance change: -132.000021 ETH (96901.999162 ETH total)
📉 Treasury outflow detected: -132.000021 ETH
🎮 Level 7: Simulating moderate completion (60%)...
🧪 ETH Test Mode: Applied 90% discount to reward: 84 ETH
🧪 ETH Test Mode: Processing level_completion reward of 84 ETH
🧪 ETH Test Mode: Level completion reward - sending 84 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xa4aa53501ac4da22853506c6422f7b2df7bebafb59c32a192220963d8d9fa90d
✅ Level 7 completed! PARTIAL: 84 ETH
❌ Level 7 completion failed: Cannot read properties of undefined (reading 'isTestMode')
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 96817999140375712541000 wei = 96817.99914037572 ETH
💰 Balance change: -84.000021 ETH (96817.999140 ETH total)
📉 Treasury outflow detected: -84.000021 ETH
🎯 Casual player casual_2 completed session
🎯 Starting grinder session: grinder_3
⏳ Grinder grinder_3 waiting 4500ms to prevent server overload...
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 96817999140375712541000 wei = 96817.99914037572 ETH
🎮 Level 1: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 79 ETH
🧪 ETH Test Mode: Processing level_completion reward of 79 ETH
🧪 ETH Test Mode: Level completion reward - sending 79 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xca00915b83e16030ec80a93f452af9fec23df65f37976d16d8036fd005335bf8
✅ Level 1 completed! PARTIAL: 79 ETH
✅ Level 1 completed! PARTIAL: 79 ETH
🎁 Game awarded 79 ETH
🎮 Level 2: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 79 ETH
🧪 ETH Test Mode: Processing level_completion reward of 79 ETH
🧪 ETH Test Mode: Level completion reward - sending 79 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x2233d97e0cc99d1d84f8438fc7c6aaaae8136cb561c0a75277c2012fa2eabc6e
✅ Level 2 completed! PARTIAL: 79 ETH
✅ Level 2 completed! PARTIAL: 79 ETH
🎁 Game awarded 79 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 96659999098005461709000 wei = 96659.99909800546 ETH
💰 Balance change: -158.000042 ETH (96659.999098 ETH total)
📉 Treasury outflow detected: -158.000042 ETH
🎮 Level 3: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 87 ETH
🧪 ETH Test Mode: Processing level_completion reward of 87 ETH
🧪 ETH Test Mode: Level completion reward - sending 87 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x45db59eb25136699dbd967866b27e1f6bf101695ea93174b0fffc66923ae59b4
✅ Level 3 completed! PARTIAL: 87 ETH
✅ Level 3 completed! PARTIAL: 87 ETH
🎁 Game awarded 87 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 96572999076854229579000 wei = 96572.99907685423 ETH
💰 Balance change: -87.000021 ETH (96572.999077 ETH total)
📉 Treasury outflow detected: -87.000021 ETH
🎮 Level 4: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 112 ETH
🧪 ETH Test Mode: Processing level_completion reward of 112 ETH
🧪 ETH Test Mode: Level completion reward - sending 112 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xe4b83b5f2d307c2e7563abf58b42f45de802eae2ce025202657d1db66cbf5370
✅ Level 4 completed! PARTIAL: 112 ETH
✅ Level 4 completed! PARTIAL: 112 ETH
🎁 Game awarded 112 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 96460999055721874979000 wei = 96460.99905572187 ETH
💰 Balance change: -112.000021 ETH (96460.999056 ETH total)
📉 Treasury outflow detected: -112.000021 ETH
🎮 Level 5: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 80 ETH
🧪 ETH Test Mode: Processing level_completion reward of 80 ETH
🧪 ETH Test Mode: Level completion reward - sending 80 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xf394685898710c6e3b6f5d31d401291ebe52ba4fa65358a8039b8b9b29b34bfd
✅ Level 5 completed! PARTIAL: 80 ETH
✅ Level 5 completed! PARTIAL: 80 ETH
🎁 Game awarded 80 ETH
🎮 Level 6: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 81 ETH
🧪 ETH Test Mode: Processing level_completion reward of 81 ETH
🧪 ETH Test Mode: Level completion reward - sending 81 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xf1f1a1e4b320e0d361c25bdfdda3eb53ea53861387c2437a69ba8f2b23404fb1
✅ Level 6 completed! PARTIAL: 81 ETH
✅ Level 6 completed! PARTIAL: 81 ETH
🎁 Game awarded 81 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 96299999013504667002000 wei = 96299.99901350467 ETH
💰 Balance change: -161.000042 ETH (96299.999014 ETH total)
📉 Treasury outflow detected: -161.000042 ETH
🎮 Level 7: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 112 ETH
🧪 ETH Test Mode: Processing level_completion reward of 112 ETH
🧪 ETH Test Mode: Level completion reward - sending 112 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xa163642a8ee7b3df3108fee7a1e65f266e6694ee280b46407ccee7323b5e6c5b
✅ Level 7 completed! PARTIAL: 112 ETH
✅ Level 7 completed! PARTIAL: 112 ETH
🎁 Game awarded 112 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 96187998992415946538000 wei = 96187.99899241595 ETH
💰 Balance change: -112.000021 ETH (96187.998992 ETH total)
📉 Treasury outflow detected: -112.000021 ETH
🎮 Level 8: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 168 ETH
🧪 ETH Test Mode: Processing level_completion reward of 168 ETH
🧪 ETH Test Mode: Level completion reward - sending 168 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x0873e9b3603ce740ce9e604c198305f67966afe2a300a634b8128e240e7df6c3
✅ Level 8 completed! PARTIAL: 168 ETH
✅ Level 8 completed! PARTIAL: 168 ETH
🎁 Game awarded 168 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 96019998971338300592000 wei = 96019.9989713383 ETH
💰 Balance change: -168.000021 ETH (96019.998971 ETH total)
📉 Treasury outflow detected: -168.000021 ETH
🎮 Level 9: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 171 ETH
🧪 ETH Test Mode: Processing level_completion reward of 171 ETH
🧪 ETH Test Mode: Level completion reward - sending 171 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x9e8b3d0510aa5f07f2e7207f5cd2f294a63ccdb7489060d2e2f210f0f11cfcde
✅ Level 9 completed! PARTIAL: 171 ETH
✅ Level 9 completed! PARTIAL: 171 ETH
🎁 Game awarded 171 ETH
🎮 Level 10: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 198 ETH
🧪 ETH Test Mode: Processing level_completion reward of 198 ETH
🧪 ETH Test Mode: Level completion reward - sending 198 ETH from hot wallet to player ******************************************
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 95848998950270346797000 wei = 95848.99895027035 ETH
💰 Balance change: -171.000021 ETH (95848.998950 ETH total)
📉 Treasury outflow detected: -171.000021 ETH
✅ ETH Test Mode: Hot wallet transaction successful: 0x9dfd1cbef3867f8928fde7b332c1c31ca1caabe727f31c58001c2528be615ed0
✅ Level 10 completed! PARTIAL: 198 ETH
✅ Level 10 completed! PARTIAL: 198 ETH
🎁 Game awarded 198 ETH
🏆 Grinder grinder_3 completed 10 levels with REAL rewards
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 95650998929210875322000 wei = 95650.*********** ETH
📊 Treasury impact after grinder_3: 4349.001071 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 95650998929210875322000 wei = 95650.*********** ETH
🔍 Raw balance: 95650998929210875322000 wei = 95650.*********** ETH
💰 Balance change: -198.000021 ETH (95650.998929 ETH total)
📉 Treasury outflow detected: -198.000021 ETH
📈 Sequential Grinding Test Results:
   Initial Balance: 100000 ETH
   Final Balance: 95650.*********** ETH
   Total Impact: 4349.001071 ETH
   Treasury Sustainable: ✅ YES
✅ Sequential grinding test completed
🎨 Running Creator Reward Test (Attack Vector 2)...
📋 Objective: Test creator reward sustainability and 50% distribution accuracy
👨‍🎨 Testing with 2 creators and 2 whales
🎨 Phase 1: Environment Creation
🎨 creator_1 creating environment: "Cyberpunk neon cityscape with rain"
💸 creator_1 spending 2500 ETH for: Reality Warp: Cyberpunk neon cityscape with rain
💸 creator_1 sending 2500 ETH to hot wallet for: Reality Warp: Cyberpunk neon cityscape with rain
🔢 Next nonce for creator_1: 0
✅ REAL blockchain transaction mined: 0xbc052ae0148bb27663cf3fde061a428600c8a2ef08cf2f3cdf9dfe321f7ffecf
⛽ Gas used: 21000
💸 creator_1 completed REAL ETH transfer: 2500 ETH to hot wallet
💰 ETH Transfer Tracked: 2500 ETH from ****************************************** to ******************************************
✅ API Call: POST /generate-environment (247ms)
🎨 Environment Creation Tracked: Cyberpunk neon cityscape Environment by creator_1
✅ Environment created by creator_1: Cyberpunk neon cityscape Environment
✅ Environment created: Cyberpunk neon cityscape Environment by creator_1
✅ Environment created: Cyberpunk neon cityscape Environment by creator_1
🎨 creator_2 creating environment: "Underwater coral reef with bioluminescence"
💸 creator_2 spending 2500 ETH for: Reality Warp: Underwater coral reef with bioluminescence
💸 creator_2 sending 2500 ETH to hot wallet for: Reality Warp: Underwater coral reef with bioluminescence
🔢 Next nonce for creator_2: 0
✅ REAL blockchain transaction mined: 0x9f9a8649bac0e5dd04fa311852491cb95bc22335c1a034034d2475db6928465c
⛽ Gas used: 21000
💸 creator_2 completed REAL ETH transfer: 2500 ETH to hot wallet
💰 ETH Transfer Tracked: 2500 ETH from ****************************************** to ******************************************
✅ API Call: POST /generate-environment (124ms)
🎨 Environment Creation Tracked: Underwater coral reef Environment by creator_2
✅ Environment created by creator_2: Underwater coral reef Environment
✅ Environment created: Underwater coral reef Environment by creator_2
✅ Environment created: Underwater coral reef Environment by creator_2
💰 Phase 2: Mystical Environment Purchases
Loaded 27 environments from server
🐋 whale_1 purchasing Mystical Environment for 10000 tokens
🎁 Expected creator reward: 5000 tokens
💰 whale_1 attempting to purchase Mystical Environment
📋 Found 5 available environments
🎯 Selected environment: Custom Environment
💰 Calculated cost for environment env_1_1756654556342: 15000 WISH → 1500 ETH (test mode)
🧪 Stress test mode: Mocking environment purchase API to prevent server restart
✅ REAL Mystical Environment purchased: Custom Environment for 1500 ETH
🎁 Creator reward distributed: 750 ETH to ******************************************
💰 ETH Transfer Tracked: 1500 ETH from ****************************************** to treasury
✅ Mystical environment purchase completed for whale_1
🐋 whale_2 purchasing Mystical Environment for 10000 tokens
🎁 Expected creator reward: 5000 tokens
💰 whale_2 attempting to purchase Mystical Environment
📋 Found 5 available environments
🎯 Selected environment: Inferno Abyss
💰 Calculated cost for environment env_3_1757189938067: 20000 WISH → 2000 ETH (test mode)
🧪 Stress test mode: Mocking environment purchase API to prevent server restart
✅ REAL Mystical Environment purchased: Inferno Abyss for 2000 ETH
🎁 Creator reward distributed: 1000 ETH to user_1757184063743_gb01gih2m
💰 ETH Transfer Tracked: 2000 ETH from ****************************************** to treasury
✅ Mystical environment purchase completed for whale_2
📊 Creator Reward Test Results:
   Total Purchases: 20000 tokens
   Expected Creator Rewards: 10000 tokens (50%)
   Environments Created: 2
   Creator Reward Accuracy: Testing 50% distribution...
✅ Creator reward test completed
👥 Running Multi-Account Coordination Test (Attack Vector 3)...
📋 Objective: Test economic balance under coordinated behavior
🤝 Simulating coordinated behavior across 5 accounts
⚡ Starting coordinated actions...
🎯 Running coordinated user 1/5...
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 100650998929210875322000 wei = 100650.*********** ETH
💰 Balance change: +5000.000000 ETH (100650.998929 ETH total)
📈 Treasury inflow detected: +5000.000000 ETH
🤝 grinder_1 starting coordinated behavior simulation
🎮 Starting grinder session for grinder_1...
🔐 Authenticated grinder_1 with server
⏳ Grinder grinder_1 waiting 1500ms to prevent server overload...
🎮 Level 1: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 59 ETH
🧪 ETH Test Mode: Processing level_completion reward of 59 ETH
🧪 ETH Test Mode: Level completion reward - sending 59 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xfb924aeb45b9c723ad0d46b3c57bf50b56db86093c897bf84f2b291b737e335f
✅ Level 1 completed! PARTIAL: 59 ETH
✅ Level 1 completed! PARTIAL: 59 ETH
🎁 Game awarded 59 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 100591998908171010140000 wei = 100591.99890817101 ETH
💰 Balance change: -59.000021 ETH (100591.998908 ETH total)
📉 Treasury outflow detected: -59.000021 ETH
🎮 Level 2: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 60 ETH
🧪 ETH Test Mode: Processing level_completion reward of 60 ETH
🧪 ETH Test Mode: Level completion reward - sending 60 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xa5c81c2cf628626a6d38853dd250b7c4d7f8fafde832eba28b3b5fa068f419a6
✅ Level 2 completed! PARTIAL: 60 ETH
✅ Level 2 completed! PARTIAL: 60 ETH
🎁 Game awarded 60 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 100531998887136121118000 wei = 100531.99888713613 ETH
💰 Balance change: -60.000021 ETH (100531.998887 ETH total)
📉 Treasury outflow detected: -60.000021 ETH
🎮 Level 3: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 104 ETH
🧪 ETH Test Mode: Processing level_completion reward of 104 ETH
🧪 ETH Test Mode: Level completion reward - sending 104 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x96ccc8fa057767ab2d7a59fa85d2c56787aca153404d67ade5bab749efdd21a0
✅ Level 3 completed! PARTIAL: 104 ETH
✅ Level 3 completed! PARTIAL: 104 ETH
🎁 Game awarded 104 ETH
🎮 Level 4: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 77 ETH
🧪 ETH Test Mode: Processing level_completion reward of 77 ETH
🧪 ETH Test Mode: Level completion reward - sending 77 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x63e718299f5da4805c42d25571a1181edf245def4e43358d9d6c854cf823aa9c
✅ Level 4 completed! PARTIAL: 77 ETH
✅ Level 4 completed! PARTIAL: 77 ETH
🎁 Game awarded 77 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 100350998845078864513000 wei = 100350.99884507887 ETH
💰 Balance change: -181.000042 ETH (100350.998845 ETH total)
📉 Treasury outflow detected: -181.000042 ETH
🎮 Level 5: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 73 ETH
🧪 ETH Test Mode: Processing level_completion reward of 73 ETH
🧪 ETH Test Mode: Level completion reward - sending 73 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xf40e62575eae11f293e959f770d475e47676d2e85b0346ff56571918f89b0cdb
✅ Level 5 completed! PARTIAL: 73 ETH
✅ Level 5 completed! PARTIAL: 73 ETH
🎁 Game awarded 73 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 100277998824055477548000 wei = 100277.99882405548 ETH
💰 Balance change: -73.000021 ETH (100277.998824 ETH total)
📉 Treasury outflow detected: -73.000021 ETH
🎮 Level 6: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 72 ETH
🧪 ETH Test Mode: Processing level_completion reward of 72 ETH
🧪 ETH Test Mode: Level completion reward - sending 72 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x4e9148c37fdd61aa2a604cd91906b97093aa1285e491cb9e6d30f6b17574b097
✅ Level 6 completed! PARTIAL: 72 ETH
✅ Level 6 completed! PARTIAL: 72 ETH
🎁 Game awarded 72 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 100205998803035009856000 wei = 100205.998803035 ETH
💰 Balance change: -72.000021 ETH (100205.998803 ETH total)
📉 Treasury outflow detected: -72.000021 ETH
🎮 Level 7: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 104 ETH
🧪 ETH Test Mode: Processing level_completion reward of 104 ETH
🧪 ETH Test Mode: Level completion reward - sending 104 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xa8d6b4211edc546f5622212db2d3b07eac2dd7363c47cea58c5b83fbd950fce1
✅ Level 7 completed! PARTIAL: 104 ETH
✅ Level 7 completed! PARTIAL: 104 ETH
🎁 Game awarded 104 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 100101998782017097024000 wei = 100101.9987820171 ETH
💰 Balance change: -104.000021 ETH (100101.998782 ETH total)
📉 Treasury outflow detected: -104.000021 ETH
🎮 Level 8: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 98 ETH
🧪 ETH Test Mode: Processing level_completion reward of 98 ETH
🧪 ETH Test Mode: Level completion reward - sending 98 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xc32f490cf073ef5e8f922e33f8a76297f27c84b5bbee8abaa2db510a092d6987
✅ Level 8 completed! PARTIAL: 98 ETH
✅ Level 8 completed! PARTIAL: 98 ETH
🎁 Game awarded 98 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 100003998761001420146000 wei = 100003.99876100142 ETH
💰 Balance change: -98.000021 ETH (100003.998761 ETH total)
📉 Treasury outflow detected: -98.000021 ETH
🎮 Level 9: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 143 ETH
🧪 ETH Test Mode: Processing level_completion reward of 143 ETH
🧪 ETH Test Mode: Level completion reward - sending 143 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x8f7743c632f8a61b718ea169b2b989da549c7a9b12988e03006b3b438e2ac4f6
✅ Level 9 completed! PARTIAL: 143 ETH
✅ Level 9 completed! PARTIAL: 143 ETH
🎁 Game awarded 143 ETH
🎮 Level 10: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 136 ETH
🧪 ETH Test Mode: Processing level_completion reward of 136 ETH
🧪 ETH Test Mode: Level completion reward - sending 136 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x975cacff181e2d0bfc4aa0ed499ff9d20d67e8aa1afc84c96dc59a3b9b5af1de
✅ Level 10 completed! PARTIAL: 136 ETH
✅ Level 10 completed! PARTIAL: 136 ETH
🎁 Game awarded 136 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 99724998718975692710000 wei = 99724.9987189757 ETH
💰 Balance change: -279.000042 ETH (99724.998719 ETH total)
📉 Treasury outflow detected: -279.000042 ETH
🏆 Grinder grinder_1 completed 10 levels with REAL rewards
✅ Completed grinder session for grinder_1
🎯 Running coordinated user 2/5...
🤝 grinder_2 starting coordinated behavior simulation
🎮 Starting grinder session for grinder_2...
🔐 Authenticated grinder_2 with server
⏳ Grinder grinder_2 waiting 3000ms to prevent server overload...
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 99724998718975692710000 wei = 99724.9987189757 ETH
🎮 Level 1: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 61 ETH
🧪 ETH Test Mode: Processing level_completion reward of 61 ETH
🧪 ETH Test Mode: Level completion reward - sending 61 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x64b754a71537854cf6048a3abccdbe02955357322a2a3d7fc3d917ce4a1c62cf
✅ Level 1 completed! PARTIAL: 61 ETH
✅ Level 1 completed! PARTIAL: 61 ETH
🎁 Game awarded 61 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 99663998697965184100000 wei = 99663.99869796519 ETH
💰 Balance change: -61.000021 ETH (99663.998698 ETH total)
📉 Treasury outflow detected: -61.000021 ETH
🎮 Level 2: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 51 ETH
🧪 ETH Test Mode: Processing level_completion reward of 51 ETH
🧪 ETH Test Mode: Level completion reward - sending 51 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x926eea109838b4815ae97e6d82b542a25b1e6894edbaa15c15efca72bdb9fd04
✅ Level 2 completed! PARTIAL: 51 ETH
✅ Level 2 completed! PARTIAL: 51 ETH
🎁 Game awarded 51 ETH
🎮 Level 3: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 101 ETH
🧪 ETH Test Mode: Processing level_completion reward of 101 ETH
🧪 ETH Test Mode: Level completion reward - sending 101 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xf2ff28f0ae7131bb14dea29eb5f3f025fb72a37abfed7f79ed5f7cbdd9131fb3
✅ Level 3 completed! PARTIAL: 101 ETH
✅ Level 3 completed! PARTIAL: 101 ETH
🎁 Game awarded 101 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 99511998655947938312000 wei = 99511.99865594794 ETH
💰 Balance change: -152.000042 ETH (99511.998656 ETH total)
📉 Treasury outflow detected: -152.000042 ETH
🎮 Level 4: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 89 ETH
🧪 ETH Test Mode: Processing level_completion reward of 89 ETH
🧪 ETH Test Mode: Level completion reward - sending 89 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x4a4424946ff4c0c8e4c247c8c6a5268a910ab7a3650c25f057641b19b7641889
✅ Level 4 completed! PARTIAL: 89 ETH
✅ Level 4 completed! PARTIAL: 89 ETH
🎁 Game awarded 89 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 99422998634940894114000 wei = 99422.9986349409 ETH
💰 Balance change: -89.000021 ETH (99422.998635 ETH total)
📉 Treasury outflow detected: -89.000021 ETH
🎮 Level 5: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 66 ETH
🧪 ETH Test Mode: Processing level_completion reward of 66 ETH
🧪 ETH Test Mode: Level completion reward - sending 66 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xa6810c645446ede35ce30b1bfb20b448c365da49f0ac65e0f48f0455c0094f93
✅ Level 5 completed! PARTIAL: 66 ETH
✅ Level 5 completed! PARTIAL: 66 ETH
🎁 Game awarded 66 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 99356998613934729207000 wei = 99356.99861393473 ETH
💰 Balance change: -66.000021 ETH (99356.998614 ETH total)
📉 Treasury outflow detected: -66.000021 ETH
🎮 Level 6: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 74 ETH
🧪 ETH Test Mode: Processing level_completion reward of 74 ETH
🧪 ETH Test Mode: Level completion reward - sending 74 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xc19c6a88337b2763c160a3986a6f1afc65ac613d6ffa7b6d34df70094ff89d87
✅ Level 6 completed! PARTIAL: 74 ETH
✅ Level 6 completed! PARTIAL: 74 ETH
🎁 Game awarded 74 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 99282998592929333824000 wei = 99282.99859292933 ETH
💰 Balance change: -74.000021 ETH (99282.998593 ETH total)
📉 Treasury outflow detected: -74.000021 ETH
🎮 Level 7: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 120 ETH
🧪 ETH Test Mode: Processing level_completion reward of 120 ETH
🧪 ETH Test Mode: Level completion reward - sending 120 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xc68c3eb1d2c9fe8052226730872617c414b9eeea6405e76624d063f345ad24a1
✅ Level 7 completed! PARTIAL: 120 ETH
✅ Level 7 completed! PARTIAL: 120 ETH
🎁 Game awarded 120 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 99162998571924611911000 wei = 99162.99857192462 ETH
💰 Balance change: -120.000021 ETH (99162.998572 ETH total)
📉 Treasury outflow detected: -120.000021 ETH
🎮 Level 8: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 150 ETH
🧪 ETH Test Mode: Processing level_completion reward of 150 ETH
🧪 ETH Test Mode: Level completion reward - sending 150 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xa91207968570e8daa1c42ee89c37a1a08093c64640e8d32085bae8687e0ff566
✅ Level 8 completed! PARTIAL: 150 ETH
✅ Level 8 completed! PARTIAL: 150 ETH
🎁 Game awarded 150 ETH
🎮 Level 9: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 170 ETH
🧪 ETH Test Mode: Processing level_completion reward of 170 ETH
🧪 ETH Test Mode: Level completion reward - sending 170 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x5d289a53eddd7bcc1f8481d81c3e92ea2531c54ae4e5849de2bf973aa1128e73
✅ Level 9 completed! PARTIAL: 170 ETH
✅ Level 9 completed! PARTIAL: 170 ETH
🎁 Game awarded 170 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 98842998529916862722000 wei = 98842.99852991686 ETH
💰 Balance change: -320.000042 ETH (98842.998530 ETH total)
📉 Treasury outflow detected: -320.000042 ETH
🎮 Level 10: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 202 ETH
🧪 ETH Test Mode: Processing level_completion reward of 202 ETH
🧪 ETH Test Mode: Level completion reward - sending 202 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x35452b18bce5ec091bbbfe80bc4bf0baed3b36e19c7214f9722434856806b52c
✅ Level 10 completed! PARTIAL: 202 ETH
✅ Level 10 completed! PARTIAL: 202 ETH
🎁 Game awarded 202 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 98640998508913697476000 wei = 98640.9985089137 ETH
💰 Balance change: -202.000021 ETH (98640.998509 ETH total)
📉 Treasury outflow detected: -202.000021 ETH
🏆 Grinder grinder_2 completed 10 levels with REAL rewards
✅ Completed grinder session for grinder_2
🎯 Running coordinated user 3/5...
🤝 grinder_3 starting coordinated behavior simulation
🎮 Starting grinder session for grinder_3...
🔐 Authenticated grinder_3 with server
⏳ Grinder grinder_3 waiting 4500ms to prevent server overload...
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 98640998508913697476000 wei = 98640.9985089137 ETH
🎮 Level 1: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 83 ETH
🧪 ETH Test Mode: Processing level_completion reward of 83 ETH
🧪 ETH Test Mode: Level completion reward - sending 83 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x4ac74491ba799823002f5500944165de509c3943c2fc968435918484e19ee00b
✅ Level 1 completed! PARTIAL: 83 ETH
✅ Level 1 completed! PARTIAL: 83 ETH
🎁 Game awarded 83 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 98557998487910927324000 wei = 98557.99848791093 ETH
💰 Balance change: -83.000021 ETH (98557.998488 ETH total)
📉 Treasury outflow detected: -83.000021 ETH
🎮 Level 2: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 65 ETH
🧪 ETH Test Mode: Processing level_completion reward of 65 ETH
🧪 ETH Test Mode: Level completion reward - sending 65 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xb438dfbf5d06520db5c928072a2987e0ae4797dca669497e0368348cc73569a1
✅ Level 2 completed! PARTIAL: 65 ETH
✅ Level 2 completed! PARTIAL: 65 ETH
🎁 Game awarded 65 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 98492998466908502937000 wei = 98492.99846690851 ETH
💰 Balance change: -65.000021 ETH (98492.998467 ETH total)
📉 Treasury outflow detected: -65.000021 ETH
🎮 Level 3: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 46 ETH
🧪 ETH Test Mode: Processing level_completion reward of 46 ETH
🧪 ETH Test Mode: Level completion reward - sending 46 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xf8d049747ff750c40818d825a644911d03d7ab79bf7c76e54c2c8200f23e211d
✅ Level 3 completed! PARTIAL: 46 ETH
✅ Level 3 completed! PARTIAL: 46 ETH
🎁 Game awarded 46 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 98446998445906381160000 wei = 98446.99844590639 ETH
💰 Balance change: -46.000021 ETH (98446.998446 ETH total)
📉 Treasury outflow detected: -46.000021 ETH
🎮 Level 4: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 60 ETH
🧪 ETH Test Mode: Processing level_completion reward of 60 ETH
🧪 ETH Test Mode: Level completion reward - sending 60 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x25613522f33d8ed359b3e4ce0b53c58efc9abeff4d4bfb7946a7b15cfe5958bc
✅ Level 4 completed! PARTIAL: 60 ETH
✅ Level 4 completed! PARTIAL: 60 ETH
🎁 Game awarded 60 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 98386998424904524214000 wei = 98386.99842490452 ETH
💰 Balance change: -60.000021 ETH (98386.998425 ETH total)
📉 Treasury outflow detected: -60.000021 ETH
🎮 Level 5: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 78 ETH
🧪 ETH Test Mode: Processing level_completion reward of 78 ETH
🧪 ETH Test Mode: Level completion reward - sending 78 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x47dea7c861a599377434532f5aa942bab770a2aea3124d7bcd159f48b84d50a8
✅ Level 5 completed! PARTIAL: 78 ETH
✅ Level 5 completed! PARTIAL: 78 ETH
🎁 Game awarded 78 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 98308998403902899045000 wei = 98308.9984039029 ETH
💰 Balance change: -78.000021 ETH (98308.998404 ETH total)
📉 Treasury outflow detected: -78.000021 ETH
🎮 Level 6: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 115 ETH
🧪 ETH Test Mode: Processing level_completion reward of 115 ETH
🧪 ETH Test Mode: Level completion reward - sending 115 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x54d2b8055c833c3aacf80b6fbd1b0a8367f04442838b3f22d01766d02347daf5
✅ Level 6 completed! PARTIAL: 115 ETH
✅ Level 6 completed! PARTIAL: 115 ETH
🎁 Game awarded 115 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 98193998382901476736000 wei = 98193.99838290148 ETH
💰 Balance change: -115.000021 ETH (98193.998383 ETH total)
📉 Treasury outflow detected: -115.000021 ETH
🎮 Level 7: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 128 ETH
🧪 ETH Test Mode: Processing level_completion reward of 128 ETH
🧪 ETH Test Mode: Level completion reward - sending 128 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x8253685885db4a655b60db0d97b1581707a0e209fac785f1b215528a86c99f65
✅ Level 7 completed! PARTIAL: 128 ETH
✅ Level 7 completed! PARTIAL: 128 ETH
🎁 Game awarded 128 ETH
🎮 Level 8: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 85 ETH
🧪 ETH Test Mode: Processing level_completion reward of 85 ETH
🧪 ETH Test Mode: Level completion reward - sending 85 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x4a94f60b85ff907d11b5782bda0d38fe1276da541a9ef085799af84b1bc794f1
✅ Level 8 completed! PARTIAL: 85 ETH
✅ Level 8 completed! PARTIAL: 85 ETH
🎁 Game awarded 85 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 97980998340899142565000 wei = 97980.99834089914 ETH
💰 Balance change: -213.000042 ETH (97980.998341 ETH total)
📉 Treasury outflow detected: -213.000042 ETH
🎮 Level 9: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 92 ETH
🧪 ETH Test Mode: Processing level_completion reward of 92 ETH
🧪 ETH Test Mode: Level completion reward - sending 92 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xee430bd935e1822bae665b60fcf519ac1abf81ebe1026c5bd2bb595cd73eedea
✅ Level 9 completed! PARTIAL: 92 ETH
✅ Level 9 completed! PARTIAL: 92 ETH
🎁 Game awarded 92 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 97888998319898189144000 wei = 97888.9983198982 ETH
💰 Balance change: -92.000021 ETH (97888.998320 ETH total)
📉 Treasury outflow detected: -92.000021 ETH
🎮 Level 10: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 125 ETH
🧪 ETH Test Mode: Processing level_completion reward of 125 ETH
🧪 ETH Test Mode: Level completion reward - sending 125 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xf5312aa9e98ebface29ba8e7ae976ca68c293b9688ef53bdfb82c3035d4cd3dc
✅ Level 10 completed! PARTIAL: 125 ETH
✅ Level 10 completed! PARTIAL: 125 ETH
🎁 Game awarded 125 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 97763998298897354730000 wei = 97763.99829889735 ETH
💰 Balance change: -125.000021 ETH (97763.998299 ETH total)
📉 Treasury outflow detected: -125.000021 ETH
🏆 Grinder grinder_3 completed 10 levels with REAL rewards
✅ Completed grinder session for grinder_3
🎯 Running coordinated user 4/5...
🤝 whale_1 starting coordinated behavior simulation
🎮 Starting whale session for whale_1...
🔐 Authenticated whale_1 with server
🛒 Extra Life: 1500 ETH (ETH test mode)
💸 whale_1 sending 1500 ETH to hot wallet for: Extra Life
🔢 Next nonce for whale_1: 0
✅ REAL blockchain transaction mined: 0x7a4c66cb8e56f1715969c500dcd28f5d6a4ee09cc60cd8db5bd62caf44322190
⛽ Gas used: 21000
💸 whale_1 completed REAL ETH transfer: 1500 ETH to hot wallet
💰 ETH Transfer Tracked: 1500 ETH from ****************************************** to ******************************************
⏳ Waiting 371.4557388873736ms before next transaction...
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 99263998298897354730000 wei = 99263.99829889735 ETH
💰 Balance change: +1500.000000 ETH (99263.998299 ETH total)
📈 Treasury inflow detected: +1500.000000 ETH
🛒 Extra Wingman: 1000 ETH (ETH test mode)
💸 whale_1 sending 1000 ETH to hot wallet for: Extra Wingman
🔢 Next nonce for whale_1: 1
✅ REAL blockchain transaction mined: 0x225730f0d9b7eb0e2d538d83482a86b1d7525701ce25e6622d92d8edeb0a7ae4
⛽ Gas used: 21000
💸 whale_1 completed REAL ETH transfer: 1000 ETH to hot wallet
💰 ETH Transfer Tracked: 1000 ETH from ****************************************** to ******************************************
⏳ Waiting 590.6801210349132ms before next transaction...
🛒 Spread Ammo: 750 ETH (ETH test mode)
💸 whale_1 sending 750 ETH to hot wallet for: Spread Ammo
🔢 Next nonce for whale_1: 2
✅ REAL blockchain transaction mined: 0xdeb4de1a4a88349b07a05cd7a9c274ed214adfc6c7c11d76ce0edbbd2a8cd4f0
⛽ Gas used: 21000
💸 whale_1 completed REAL ETH transfer: 750 ETH to hot wallet
💰 ETH Transfer Tracked: 750 ETH from ****************************************** to ******************************************
⏳ Waiting 751.4216406930504ms before next transaction...
🎮 Level 1: Simulating minimal effort completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 54 ETH
🧪 ETH Test Mode: Processing level_completion reward of 54 ETH
🧪 ETH Test Mode: Level completion reward - sending 54 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x4e38c4cb7d6061e48728a7dc904c116153160b2ac6ad7636ea5e99bb358873af
✅ Level 1 completed! PARTIAL: 54 ETH
❌ Level 1 completion failed: Cannot read properties of undefined (reading 'isTestMode')
🎮 Level 2: Simulating minimal effort completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 50 ETH
🧪 ETH Test Mode: Processing level_completion reward of 50 ETH
🧪 ETH Test Mode: Level completion reward - sending 50 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x1a4194e0ee6b9295962ffe6861b350f29da7aed4a94a49d4ad81a7e617d4aab4
✅ Level 2 completed! PARTIAL: 50 ETH
❌ Level 2 completion failed: Cannot read properties of undefined (reading 'isTestMode')
💰 Whale whale_1 completed session with heavy spending
✅ Completed whale session for whale_1
🎯 Running coordinated user 5/5...
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 100909998256896436715000 wei = 100909.99825689643 ETH
💰 Balance change: +1645.999958 ETH (100909.998257 ETH total)
📈 Treasury inflow detected: +1645.999958 ETH
🤝 whale_2 starting coordinated behavior simulation
🎮 Starting whale session for whale_2...
🔐 Authenticated whale_2 with server
🛒 Extra Life: 1500 ETH (ETH test mode)
💸 whale_2 sending 1500 ETH to hot wallet for: Extra Life
🔢 Next nonce for whale_2: 0
✅ REAL blockchain transaction mined: 0xbaadd156d08bc3e6cd7363b245f3c8df2eef005f01004488162ecc2c1d646080
⛽ Gas used: 21000
💸 whale_2 completed REAL ETH transfer: 1500 ETH to hot wallet
💰 ETH Transfer Tracked: 1500 ETH from ****************************************** to ******************************************
⏳ Waiting 303.38981776096125ms before next transaction...
🛒 Extra Wingman: 1000 ETH (ETH test mode)
💸 whale_2 sending 1000 ETH to hot wallet for: Extra Wingman
🔢 Next nonce for whale_2: 1
✅ REAL blockchain transaction mined: 0x0a47db681348e08300e62cc77b78c7f66c09f3a02284df20c54986e95e936f3a
⛽ Gas used: 21000
💸 whale_2 completed REAL ETH transfer: 1000 ETH to hot wallet
💰 ETH Transfer Tracked: 1000 ETH from ****************************************** to ******************************************
⏳ Waiting 522.6084308742571ms before next transaction...
🛒 Spread Ammo: 750 ETH (ETH test mode)
💸 whale_2 sending 750 ETH to hot wallet for: Spread Ammo
🔢 Next nonce for whale_2: 2
✅ REAL blockchain transaction mined: 0xb1568bdd2091a02eca0ac5045a50196176b14a324478ca7031b94350dfbda77d
⛽ Gas used: 21000
💸 whale_2 completed REAL ETH transfer: 750 ETH to hot wallet
💰 ETH Transfer Tracked: 750 ETH from ****************************************** to ******************************************
⏳ Waiting 738.361493659916ms before next transaction...
🎮 Level 1: Simulating minimal effort completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 78 ETH
🧪 ETH Test Mode: Processing level_completion reward of 78 ETH
🧪 ETH Test Mode: Level completion reward - sending 78 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x820e24ca7216ef575041ac8fce747c897510d2b7b68567a0701ba1b5d20ce7f8
✅ Level 1 completed! PARTIAL: 78 ETH
❌ Level 1 completion failed: Cannot read properties of undefined (reading 'isTestMode')
🎮 Level 2: Simulating minimal effort completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 77 ETH
🧪 ETH Test Mode: Processing level_completion reward of 77 ETH
🧪 ETH Test Mode: Level completion reward - sending 77 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xb70cfbccd7a9c682fa102cdac864268370add29838653d376f978886268d18d5
✅ Level 2 completed! PARTIAL: 77 ETH
❌ Level 2 completion failed: Cannot read properties of undefined (reading 'isTestMode')
💰 Whale whale_2 completed session with heavy spending
✅ Completed whale session for whale_2
📊 Multi-Account Coordination Results:
   Coordination Duration: 150211ms
   Accounts Coordinated: 5
   Treasury Impact: Analyzing...
✅ Multi-account coordination test completed
💰 Running Treasury Drain Test (Maximum Stress)...
📋 Objective: Test maximum stress on treasury with all users acting simultaneously
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 104004998214895965307000 wei = 104004.*********** ETH
💰 Pre-stress treasury balance: 104004.*********** ETH
⚡ Initiating maximum stress scenario...
🎯 Running stress test user 1/10...
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 104004998214895965307000 wei = 104004.*********** ETH
💰 Balance change: +3094.999958 ETH (104004.998215 ETH total)
📈 Treasury inflow detected: +3094.999958 ETH
⚡ grinder_1 starting maximum stress behavior
⏳ Grinder grinder_1 waiting 1500ms to prevent server overload...
🎮 Level 1: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 70 ETH
🧪 ETH Test Mode: Processing level_completion reward of 70 ETH
🧪 ETH Test Mode: Level completion reward - sending 70 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x429e1987a2dedd1628f19bfb68ea9b7b82e8af83e34cafd8f6eec131f4ef0686
✅ Level 1 completed! PARTIAL: 70 ETH
✅ Level 1 completed! PARTIAL: 70 ETH
🎁 Game awarded 70 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 103934998193895772737000 wei = 103934.99819389578 ETH
💰 Balance change: -70.000021 ETH (103934.998194 ETH total)
📉 Treasury outflow detected: -70.000021 ETH
🎮 Level 2: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 43 ETH
🧪 ETH Test Mode: Processing level_completion reward of 43 ETH
🧪 ETH Test Mode: Level completion reward - sending 43 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x1453084981bbc04b84c1e34f643a09e760702fb720717b90d48c9df4da3659ab
✅ Level 2 completed! PARTIAL: 43 ETH
✅ Level 2 completed! PARTIAL: 43 ETH
🎁 Game awarded 43 ETH
🎮 Level 3: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 62 ETH
🧪 ETH Test Mode: Processing level_completion reward of 62 ETH
🧪 ETH Test Mode: Level completion reward - sending 62 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xfb65805057d1aeb86b7722a93b5aedf2b48455396c3edc2a795089f5cc7da16c
✅ Level 3 completed! PARTIAL: 62 ETH
✅ Level 3 completed! PARTIAL: 62 ETH
🎁 Game awarded 62 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 103829998151895456666000 wei = 103829.99815189546 ETH
💰 Balance change: -105.000042 ETH (103829.998152 ETH total)
📉 Treasury outflow detected: -105.000042 ETH
🎮 Level 4: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 100 ETH
🧪 ETH Test Mode: Processing level_completion reward of 100 ETH
🧪 ETH Test Mode: Level completion reward - sending 100 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x524db10cd13a5be72f729e142cc6c9572d93990f7995d846e8959f9ba6270de8
✅ Level 4 completed! PARTIAL: 100 ETH
✅ Level 4 completed! PARTIAL: 100 ETH
🎁 Game awarded 100 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 103729998130895327537000 wei = 103729.99813089533 ETH
💰 Balance change: -100.000021 ETH (103729.998131 ETH total)
📉 Treasury outflow detected: -100.000021 ETH
🎮 Level 5: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 87 ETH
🧪 ETH Test Mode: Processing level_completion reward of 87 ETH
🧪 ETH Test Mode: Level completion reward - sending 87 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xdde9587aea48e40174c026ac5b3b041e208180b2199cc69d30a4bc2789479f36
✅ Level 5 completed! PARTIAL: 87 ETH
✅ Level 5 completed! PARTIAL: 87 ETH
🎁 Game awarded 87 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 103642998109895214515000 wei = 103642.99810989521 ETH
💰 Balance change: -87.000021 ETH (103642.998110 ETH total)
📉 Treasury outflow detected: -87.000021 ETH
🎮 Level 6: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 68 ETH
🧪 ETH Test Mode: Processing level_completion reward of 68 ETH
🧪 ETH Test Mode: Level completion reward - sending 68 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x41cf975ff0a3681a1b6bf4eebdf6130f1e44732b28bf766fe695ab546e0a8ca3
✅ Level 6 completed! PARTIAL: 68 ETH
✅ Level 6 completed! PARTIAL: 68 ETH
🎁 Game awarded 68 ETH
🎮 Level 7: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 140 ETH
🧪 ETH Test Mode: Processing level_completion reward of 140 ETH
🧪 ETH Test Mode: Level completion reward - sending 140 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x069b70ab711be5026baae774de07e9b1f3163e788a417da5a05675a96c5d6cc0
✅ Level 7 completed! PARTIAL: 140 ETH
✅ Level 7 completed! PARTIAL: 140 ETH
🎁 Game awarded 140 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 103434998067895029001000 wei = 103434.99806789502 ETH
💰 Balance change: -208.000042 ETH (103434.998068 ETH total)
📉 Treasury outflow detected: -208.000042 ETH
🎮 Level 8: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 80 ETH
🧪 ETH Test Mode: Processing level_completion reward of 80 ETH
🧪 ETH Test Mode: Level completion reward - sending 80 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xd7ac27ff5dea96ee0cb51d7bf4e2d899763ac9eb4300f58b2ce9f16203186d40
✅ Level 8 completed! PARTIAL: 80 ETH
✅ Level 8 completed! PARTIAL: 80 ETH
🎁 Game awarded 80 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 103354998046894953212000 wei = 103354.99804689495 ETH
💰 Balance change: -80.000021 ETH (103354.998047 ETH total)
📉 Treasury outflow detected: -80.000021 ETH
🎮 Level 9: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 120 ETH
🧪 ETH Test Mode: Processing level_completion reward of 120 ETH
🧪 ETH Test Mode: Level completion reward - sending 120 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x34b61afd361fab7c499c90c7715f9658e4259700da44a9d9b5254a3248bdeffc
✅ Level 9 completed! PARTIAL: 120 ETH
✅ Level 9 completed! PARTIAL: 120 ETH
🎁 Game awarded 120 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 103234998025894886873000 wei = 103234.99802589489 ETH
💰 Balance change: -120.000021 ETH (103234.998026 ETH total)
📉 Treasury outflow detected: -120.000021 ETH
🎮 Level 10: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 139 ETH
🧪 ETH Test Mode: Processing level_completion reward of 139 ETH
🧪 ETH Test Mode: Level completion reward - sending 139 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xcee7ff72d82a119f93fc57ef299304ae7d782caaf8899897f44295d353bd0c2b
✅ Level 10 completed! PARTIAL: 139 ETH
✅ Level 10 completed! PARTIAL: 139 ETH
🎁 Game awarded 139 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 103095998004894828808000 wei = 103095.99800489483 ETH
💰 Balance change: -139.000021 ETH (103095.998005 ETH total)
📉 Treasury outflow detected: -139.000021 ETH
🏆 Grinder grinder_1 completed 10 levels with REAL rewards
🎯 Running stress test user 2/10...
⚡ grinder_2 starting maximum stress behavior
⏳ Grinder grinder_2 waiting 3000ms to prevent server overload...
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 103095998004894828808000 wei = 103095.99800489483 ETH
🎮 Level 1: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 79 ETH
🧪 ETH Test Mode: Processing level_completion reward of 79 ETH
🧪 ETH Test Mode: Level completion reward - sending 79 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x3381bae8fffaf78fdbfd45287f975fcfa72934a071993f8cb9e5d9a856790314
✅ Level 1 completed! PARTIAL: 79 ETH
✅ Level 1 completed! PARTIAL: 79 ETH
🎁 Game awarded 79 ETH
🎮 Level 2: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 82 ETH
🧪 ETH Test Mode: Processing level_completion reward of 82 ETH
🧪 ETH Test Mode: Level completion reward - sending 82 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x6b7ef408be1563716a43c2bd9ffa1a5c1b9dfcc41c92f3901400cee1c285c18c
✅ Level 2 completed! PARTIAL: 82 ETH
✅ Level 2 completed! PARTIAL: 82 ETH
🎁 Game awarded 82 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 102934997962894733510000 wei = 102934.99796289473 ETH
💰 Balance change: -161.000042 ETH (102934.997963 ETH total)
📉 Treasury outflow detected: -161.000042 ETH
🎮 Level 3: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 46 ETH
🧪 ETH Test Mode: Processing level_completion reward of 46 ETH
🧪 ETH Test Mode: Level completion reward - sending 46 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x35e7f2d3128697e06e105cc1e7017988d689f0159cff0213e4694f17f7427f6b
✅ Level 3 completed! PARTIAL: 46 ETH
✅ Level 3 completed! PARTIAL: 46 ETH
🎁 Game awarded 46 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 102888997941894694576000 wei = 102888.99794189469 ETH
💰 Balance change: -46.000021 ETH (102888.997942 ETH total)
📉 Treasury outflow detected: -46.000021 ETH
🎮 Level 4: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 66 ETH
🧪 ETH Test Mode: Processing level_completion reward of 66 ETH
🧪 ETH Test Mode: Level completion reward - sending 66 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x4dd2835988ef918bd8c873902d68c31e9a0262b07da9279ce2d961949f6d1064
✅ Level 4 completed! PARTIAL: 66 ETH
✅ Level 4 completed! PARTIAL: 66 ETH
🎁 Game awarded 66 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 102822997920894660493000 wei = 102822.99792089466 ETH
💰 Balance change: -66.000021 ETH (102822.997921 ETH total)
📉 Treasury outflow detected: -66.000021 ETH
🎮 Level 5: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 72 ETH
🧪 ETH Test Mode: Processing level_completion reward of 72 ETH
🧪 ETH Test Mode: Level completion reward - sending 72 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x6c320673ddf8f89ece70bc3912bc020fa5863c223e4c729eefb7aa808620c539
✅ Level 5 completed! PARTIAL: 72 ETH
✅ Level 5 completed! PARTIAL: 72 ETH
🎁 Game awarded 72 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 102750997899894630652000 wei = 102750.99789989463 ETH
💰 Balance change: -72.000021 ETH (102750.997900 ETH total)
📉 Treasury outflow detected: -72.000021 ETH
🎮 Level 6: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 125 ETH
🧪 ETH Test Mode: Processing level_completion reward of 125 ETH
🧪 ETH Test Mode: Level completion reward - sending 125 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x6ff6c5654e3b11a4eb6d6e9b032b2ebbe78e891d6ce8b023746c5480b03aaa34
✅ Level 6 completed! PARTIAL: 125 ETH
✅ Level 6 completed! PARTIAL: 125 ETH
🎁 Game awarded 125 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 102625997878894604528000 wei = 102625.9978788946 ETH
💰 Balance change: -125.000021 ETH (102625.997879 ETH total)
📉 Treasury outflow detected: -125.000021 ETH
🎮 Level 7: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 106 ETH
🧪 ETH Test Mode: Processing level_completion reward of 106 ETH
🧪 ETH Test Mode: Level completion reward - sending 106 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x1d960c0c952e22d9a49644ab5410c5716d0a963f6505d16b29a93e40a4721282
✅ Level 7 completed! PARTIAL: 106 ETH
✅ Level 7 completed! PARTIAL: 106 ETH
🎁 Game awarded 106 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 102519997857894581659000 wei = 102519.99785789459 ETH
💰 Balance change: -106.000021 ETH (102519.997858 ETH total)
📉 Treasury outflow detected: -106.000021 ETH
🎮 Level 8: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 121 ETH
🧪 ETH Test Mode: Processing level_completion reward of 121 ETH
🧪 ETH Test Mode: Level completion reward - sending 121 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x71b4df202576063d3c76d227ed29cd22704df573e42efafcaf4c7341b210393a
✅ Level 8 completed! PARTIAL: 121 ETH
✅ Level 8 completed! PARTIAL: 121 ETH
🎁 Game awarded 121 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 102398997836894561625000 wei = 102398.99783689456 ETH
💰 Balance change: -121.000021 ETH (102398.997837 ETH total)
📉 Treasury outflow detected: -121.000021 ETH
🎮 Level 9: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 147 ETH
🧪 ETH Test Mode: Processing level_completion reward of 147 ETH
🧪 ETH Test Mode: Level completion reward - sending 147 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x0a7de478a88c7222fd33a7021f57aaead77364e70116695b494c32c633a092ff
✅ Level 9 completed! PARTIAL: 147 ETH
✅ Level 9 completed! PARTIAL: 147 ETH
🎁 Game awarded 147 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 102251997815894544090000 wei = 102251.99781589455 ETH
💰 Balance change: -147.000021 ETH (102251.997816 ETH total)
📉 Treasury outflow detected: -147.000021 ETH
🎮 Level 10: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 153 ETH
🧪 ETH Test Mode: Processing level_completion reward of 153 ETH
🧪 ETH Test Mode: Level completion reward - sending 153 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x00f124dbb1943dd5427ccd1efb89c5f1e6058298cea02fa015e9d80eb8c8379a
✅ Level 10 completed! PARTIAL: 153 ETH
✅ Level 10 completed! PARTIAL: 153 ETH
🎁 Game awarded 153 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 102098997794894528739000 wei = 102098.99779489453 ETH
💰 Balance change: -153.000021 ETH (102098.997795 ETH total)
📉 Treasury outflow detected: -153.000021 ETH
🏆 Grinder grinder_2 completed 10 levels with REAL rewards
🎯 Running stress test user 3/10...
⚡ grinder_3 starting maximum stress behavior
⏳ Grinder grinder_3 waiting 4500ms to prevent server overload...
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 102098997794894528739000 wei = 102098.99779489453 ETH
🎮 Level 1: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 49 ETH
🧪 ETH Test Mode: Processing level_completion reward of 49 ETH
🧪 ETH Test Mode: Level completion reward - sending 49 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xf7faffd9db0deb7417ca294083dbb9115285942a1a231f5f71dbe7b5b1289f38
✅ Level 1 completed! PARTIAL: 49 ETH
✅ Level 1 completed! PARTIAL: 49 ETH
🎁 Game awarded 49 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 102049997773894515299000 wei = 102049.99777389452 ETH
💰 Balance change: -49.000021 ETH (102049.997774 ETH total)
📉 Treasury outflow detected: -49.000021 ETH
🎮 Level 2: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 48 ETH
🧪 ETH Test Mode: Processing level_completion reward of 48 ETH
🧪 ETH Test Mode: Level completion reward - sending 48 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xdf8877fb3e4271f67376b20655614757df551f1d8524eb0654d0df20308f4f0b
✅ Level 2 completed! PARTIAL: 48 ETH
✅ Level 2 completed! PARTIAL: 48 ETH
🎁 Game awarded 48 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 102001997752894503518000 wei = 102001.9977528945 ETH
💰 Balance change: -48.000021 ETH (102001.997753 ETH total)
📉 Treasury outflow detected: -48.000021 ETH
🎮 Level 3: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 72 ETH
🧪 ETH Test Mode: Processing level_completion reward of 72 ETH
🧪 ETH Test Mode: Level completion reward - sending 72 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x7d913f4d29962e79fe8a98ab5229cbf2841a215e63a435be4160b8fed68b6fe0
✅ Level 3 completed! PARTIAL: 72 ETH
✅ Level 3 completed! PARTIAL: 72 ETH
🎁 Game awarded 72 ETH
🎮 Level 4: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 73 ETH
🧪 ETH Test Mode: Processing level_completion reward of 73 ETH
🧪 ETH Test Mode: Level completion reward - sending 73 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xc64e1e40554d004e0544d49a7fbd512ad5ab89536ecab825ea027742c3edf4e5
✅ Level 4 completed! PARTIAL: 73 ETH
✅ Level 4 completed! PARTIAL: 73 ETH
🎁 Game awarded 73 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 101856997710894484177000 wei = 101856.99771089449 ETH
💰 Balance change: -145.000042 ETH (101856.997711 ETH total)
📉 Treasury outflow detected: -145.000042 ETH
🎮 Level 5: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 81 ETH
🧪 ETH Test Mode: Processing level_completion reward of 81 ETH
🧪 ETH Test Mode: Level completion reward - sending 81 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x876ae3af5d5a31a9a532592638f4ea56f299c8d0c6e162e07001cfbf2a62caca
✅ Level 5 completed! PARTIAL: 81 ETH
✅ Level 5 completed! PARTIAL: 81 ETH
🎁 Game awarded 81 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 101775997689894476260000 wei = 101775.99768989447 ETH
💰 Balance change: -81.000021 ETH (101775.997690 ETH total)
📉 Treasury outflow detected: -81.000021 ETH
🎮 Level 6: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 130 ETH
🧪 ETH Test Mode: Processing level_completion reward of 130 ETH
🧪 ETH Test Mode: Level completion reward - sending 130 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x4924342efb3497e461766de497df6ae37b1ee7e66cbfcc66f167539dcb7df967
✅ Level 6 completed! PARTIAL: 130 ETH
✅ Level 6 completed! PARTIAL: 130 ETH
🎁 Game awarded 130 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 101645997668894469330000 wei = 101645.99766889447 ETH
💰 Balance change: -130.000021 ETH (101645.997669 ETH total)
📉 Treasury outflow detected: -130.000021 ETH
🎮 Level 7: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 90 ETH
🧪 ETH Test Mode: Processing level_completion reward of 90 ETH
🧪 ETH Test Mode: Level completion reward - sending 90 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x79df47abdfa9002baf5e2a2b079e80feba2664808fedc14b1f04663af97de783
✅ Level 7 completed! PARTIAL: 90 ETH
✅ Level 7 completed! PARTIAL: 90 ETH
🎁 Game awarded 90 ETH
🎮 Level 8: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 160 ETH
🧪 ETH Test Mode: Processing level_completion reward of 160 ETH
🧪 ETH Test Mode: Level completion reward - sending 160 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xc56e2eef853c1c5e0ccb5e6819da9ffd370eb117cb0aeb7cb29f769fec45201c
✅ Level 8 completed! PARTIAL: 160 ETH
✅ Level 8 completed! PARTIAL: 160 ETH
🎁 Game awarded 160 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 101395997626894457948000 wei = 101395.99762689446 ETH
💰 Balance change: -250.000042 ETH (101395.997627 ETH total)
📉 Treasury outflow detected: -250.000042 ETH
🎮 Level 9: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 168 ETH
🧪 ETH Test Mode: Processing level_completion reward of 168 ETH
🧪 ETH Test Mode: Level completion reward - sending 168 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0xdc4310e16307b850284f19e5777dfbfc8f305274c5d029b127c10eaae4ffacfc
✅ Level 9 completed! PARTIAL: 168 ETH
✅ Level 9 completed! PARTIAL: 168 ETH
🎁 Game awarded 168 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 101227997605894453286000 wei = 101227.99760589446 ETH
💰 Balance change: -168.000021 ETH (101227.997606 ETH total)
📉 Treasury outflow detected: -168.000021 ETH
🎮 Level 10: Simulating completion...
🧪 ETH Test Mode: Applied 90% discount to reward: 107 ETH
🧪 ETH Test Mode: Processing level_completion reward of 107 ETH
🧪 ETH Test Mode: Level completion reward - sending 107 ETH from hot wallet to player ******************************************
✅ ETH Test Mode: Hot wallet transaction successful: 0x0bb69e04ab4bcd216608755a5fb95eae01d3f7b0bbdc8c2f8a83c1e6842cd6e0
✅ Level 10 completed! PARTIAL: 107 ETH
✅ Level 10 completed! PARTIAL: 107 ETH
🎁 Game awarded 107 ETH
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 101120997584894449191000 wei = 101120.99758489445 ETH
💰 Balance change: -107.000021 ETH (101120.997585 ETH total)
📉 Treasury outflow detected: -107.000021 ETH
🏆 Grinder grinder_3 completed 10 levels with REAL rewards
🎯 Running stress test user 4/10...
⚡ whale_1 starting maximum stress behavior
🛒 Extra Life: 1500 ETH (ETH test mode)
💸 whale_1 sending 1500 ETH to hot wallet for: Extra Life
🔢 Next nonce for whale_1: 3
✅ REAL blockchain transaction mined: 0x9ec3db2ac84c05553abbeab477d59413bd1cf03a5ff07c4675c8a8c276e5ca3d
⛽ Gas used: 21000
💸 whale_1 completed REAL ETH transfer: 1500 ETH to hot wallet
💰 ETH Transfer Tracked: 1500 ETH from ****************************************** to ******************************************
⏳ Waiting 305.0563759734469ms before next transaction...
🛒 Extra Wingman: 1000 ETH (ETH test mode)
💸 whale_1 sending 1000 ETH to hot wallet for: Extra Wingman
🔢 Next nonce for whale_1: 4
✅ REAL blockchain transaction mined: 0xac6540c46cc3ceb448f224a66fc51d02dffeefff884eaac4272c35fbe076d678
⛽ Gas used: 21000
💸 whale_1 completed REAL ETH transfer: 1000 ETH to hot wallet
💰 ETH Transfer Tracked: 1000 ETH from ****************************************** to ******************************************
⏳ Waiting 548.2872831316945ms before next transaction...
🛒 Spread Ammo: 750 ETH (ETH test mode)
💸 whale_1 sending 750 ETH to hot wallet for: Spread Ammo
🔢 Next nonce for whale_1: 5
✅ REAL blockchain transaction mined: 0x85e44f1783d06a78cc84ea1fb5540d685ff4327bc460d6813eb6a08e0cd6ae2b
⛽ Gas used: 21000
💸 whale_1 completed REAL ETH transfer: 750 ETH to hot wallet
💰 ETH Transfer Tracked: 750 ETH from ****************************************** to ******************************************
⏳ Waiting 732.2338577392921ms before next transaction...
🎯 Running stress test user 5/10...
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 104370997584894449191000 wei = 104370.99758489445 ETH
💰 Balance change: +3250.000000 ETH (104370.997585 ETH total)
📈 Treasury inflow detected: +3250.000000 ETH
⚡ whale_2 starting maximum stress behavior
🛒 Extra Life: 1500 ETH (ETH test mode)
💸 whale_2 sending 1500 ETH to hot wallet for: Extra Life
🔢 Next nonce for whale_2: 3
✅ REAL blockchain transaction mined: 0xe61acdcb45acb93420468a56f70f847ad075a70d5ca7a7868f1f62ea48f80960
⛽ Gas used: 21000
💸 whale_2 completed REAL ETH transfer: 1500 ETH to hot wallet
💰 ETH Transfer Tracked: 1500 ETH from ****************************************** to ******************************************
⏳ Waiting 316.7533745900705ms before next transaction...
🛒 Extra Wingman: 1000 ETH (ETH test mode)
💸 whale_2 sending 1000 ETH to hot wallet for: Extra Wingman
🔢 Next nonce for whale_2: 4
✅ REAL blockchain transaction mined: 0xa7ba1e0f7ebc618c07d76b7a83ebe75d46be57e3676d6e02d0d868ac443141c0
⛽ Gas used: 21000
💸 whale_2 completed REAL ETH transfer: 1000 ETH to hot wallet
💰 ETH Transfer Tracked: 1000 ETH from ****************************************** to ******************************************
⏳ Waiting 517.9194877193511ms before next transaction...
🛒 Spread Ammo: 750 ETH (ETH test mode)
💸 whale_2 sending 750 ETH to hot wallet for: Spread Ammo
🔢 Next nonce for whale_2: 5
✅ REAL blockchain transaction mined: 0x7dc6b096240e3f7eab240ed5dce916890c7b48af20e7fcfbc00567006fa927e0
⛽ Gas used: 21000
💸 whale_2 completed REAL ETH transfer: 750 ETH to hot wallet
💰 ETH Transfer Tracked: 750 ETH from ****************************************** to ******************************************
⏳ Waiting 783.8049509429089ms before next transaction...
🎯 Running stress test user 6/10...
⚡ creator_1 starting maximum stress behavior
🎨 creator_1 creating environment: "Ancient temple ruins in a jungle"
💸 creator_1 spending 2500 ETH for: Reality Warp: Ancient temple ruins in a jungle
💸 creator_1 sending 2500 ETH to hot wallet for: Reality Warp: Ancient temple ruins in a jungle
🔢 Next nonce for creator_1: 1
✅ REAL blockchain transaction mined: 0x7806b830c14b82586788c1a6dba9c010f30f011ae6a7e626fcf9939270397fef
⛽ Gas used: 21000
💸 creator_1 completed REAL ETH transfer: 2500 ETH to hot wallet
💰 ETH Transfer Tracked: 2500 ETH from ****************************************** to ******************************************
✅ API Call: POST /generate-environment (156ms)
🎨 Environment Creation Tracked: Ancient temple ruins Environment by creator_1
✅ Environment created by creator_1: Ancient temple ruins Environment
✅ Environment created: Ancient temple ruins Environment by creator_1
🎯 Running stress test user 7/10...
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 110120997584894449191000 wei = 110120.99758489445 ETH
💰 Balance change: +5750.000000 ETH (110120.997585 ETH total)
📈 Treasury inflow detected: +5750.000000 ETH
⚡ creator_2 starting maximum stress behavior
🎨 creator_2 creating environment: "Ice planet with aurora borealis"
💸 creator_2 spending 2500 ETH for: Reality Warp: Ice planet with aurora borealis
💸 creator_2 sending 2500 ETH to hot wallet for: Reality Warp: Ice planet with aurora borealis
🔢 Next nonce for creator_2: 1
✅ REAL blockchain transaction mined: 0xe2e68df4c95a35f2ec83dc98fb0a1078e5b2a4d02967b8397982fe6fff42ea08
⛽ Gas used: 21000
💸 creator_2 completed REAL ETH transfer: 2500 ETH to hot wallet
💰 ETH Transfer Tracked: 2500 ETH from ****************************************** to ******************************************
✅ API Call: POST /generate-environment (102ms)
🎨 Environment Creation Tracked: Ice planet with Environment by creator_2
✅ Environment created by creator_2: Ice planet with Environment
✅ Environment created: Ice planet with Environment by creator_2
🎯 Running stress test user 8/10...
⚡ casual_1 starting maximum stress behavior
🎯 Running stress test user 9/10...
⚡ casual_2 starting maximum stress behavior
🎯 Running stress test user 10/10...
⚡ casual_3 starting maximum stress behavior
🔍 Reading balance for hot wallet: ******************************************
🔍 Raw balance: 112620997584894449191000 wei = 112620.99758489445 ETH
📊 Treasury Drain Test Results:
   Stress Duration: 149572ms
   Users Participating: 10
   Initial Balance: 104004.*********** ETH
   Final Balance: 112620.99758489445 ETH
   Total Drain: -8615.999370 ETH
   Drain Percentage: -8.28%
   Treasury Survived: ✅ YES
✅ Treasury drain test completed
🛑 TreasuryMonitor stopped
📊 Monitored for 494186ms
💰 Final balance: 110120.99758489445 ETH
📈 Balance change: 10120.997585 ETH
🛑 TransactionTracker stopped
📊 Tracked 22 transactions in 494187ms
📊 Monitoring stopped
🔍 Queue health monitoring stopped
🔍 Running comprehensive validation...
🔍 Starting comprehensive validation...
💰 Validating treasury sustainability...
🔍 Treasury Status Debug:
🎨 Validating creator reward distribution...
🔍 Creator Analysis Debug:
🎮 Validating grinder behavior...
🔍 User Activity Debug:
🔗 Validating transaction integrity...
🔍 Transaction Data Debug:
⚖️ Validating economic balance...
🔧 Validating system stability...

🔍 VALIDATION RESULTS
==================================================
Overall Result: ✅ PASS
Tests Passed: 14/17 (82.4%)
Critical Issues: 0
Warnings: 2

⚠️ WARNINGS:
   1. Creator Rewards Distributed: 0 ETH (expected: > 0 ETH)
   2. Grinder Users Present: 0 grinders (expected: > 0 grinders (when user activity is tracked))

💡 RECOMMENDATIONS:
   1. Review warning items for potential improvements

✅ Validation completed
📊 Test Report Generated
🧹 Cleaning up rate limiter resources...
📊 Final rate limiter status: {}
✅ Rate limiter cleanup completed
✅ Stress test completed
🧹 Cleaning up rate limiter resources...
📊 Final rate limiter status: {}
✅ Rate limiter cleanup completed

📊 STRESS TEST RESULTS
============================================================
📋 Test Summary:
   Total Duration: 498.238 seconds
   Users Simulated: 10
   Total Transactions: 22
   Test Completion: Full duration

💰 Treasury Analysis:
   Initial Balance: 100000 ETH
   Final Balance: 110120.99758489445 ETH
   Balance Change: 10120.997584894445 ETH
   Net Flow: 10120.997584894445 ETH
   Risk Level: low

🎨 Creator Reward Analysis:
   Total Creator Rewards: 0 ETH
   Average Reward per Environment: 0 ETH
   Reward Distribution Accuracy: 100%

📈 Sustainability Assessment:
   Is Sustainable: ✅ YES
   Risk Level: low
   Projected Runtime: undefined seconds

💡 Recommendations:
   1. Treasury balance stable
   2. Current tokenomics parameters appear sustainable
   3. Positive net flow - treasury is growing

✅ Stress test completed successfully